﻿using System;
using System.Collections.Generic;
using Microsoft.Exchange.WebServices.Data;
using System.Threading.Tasks;
using Microsoft.Identity.Client;
using CPHI.Spark.Repository;
using System.Linq;
using log4net;
using CPHI.Spark.Model;
using log4net.Repository.Hierarchy;

namespace CPHI.Spark.Reporter
{
   public static class EmailerService
   {

      public async static System.Threading.Tasks.Task SendMailOnError(DealerGroupName? dealerGroupName, string subjectText, Exception ex)
      {
         string messageText = ex.Message;
         messageText += ex.StackTrace;

         await SendMailOnError(dealerGroupName, subjectText, messageText);

         return;
      }


      public async static System.Threading.Tasks.Task SendMailOnError(DealerGroupName? dealerGroupName, string subjectText, string messageText)
      {
         List<string> tos = ConfigService.ErrorEmailTo.ToList();

         var service = await ProvideExchangeService();

         EmailMessage message = new EmailMessage(service);
         message.Body = new MessageBody(BodyType.HTML, messageText);

         message.Subject = (dealerGroupName != null) ? dealerGroupName.ToString() + ": " : string.Empty;
         message.Subject += subjectText;


         message.ToRecipients.AddRange(tos);

         if (ConfigService.ServiceName.Contains("Dev")) { message.Subject += "_Dev"; }

         try
         {
            await message.SendAndSaveCopy();
         }
         catch (Exception e)
         {
            throw new Exception(messageText, e);
            { }
         }

         return;
      }

      public async static System.Threading.Tasks.Task SendMailOldStyle(DealerGroupName customer, string subjectText, string messageText, string fileAttach, ILog logger, List<string> tos = null, List<string> ccs = null)
      {
         var service = await ProvideExchangeService();

         EmailMessage message = new EmailMessage(service);
         message.Body = new MessageBody(BodyType.HTML, messageText);
         message.Subject = subjectText;

         if (tos != null)
         {
            message.ToRecipients.AddRange(tos);
         }
         else
         {
            message.ToRecipients.Add("<EMAIL>");
         }

         if (ccs != null)
         {
            message.CcRecipients.AddRange(ccs);
         }

         if (ConfigService.ServiceName.Contains("Dev"))
         {
            message.Subject += "_Dev";
         }

         if (fileAttach != null)
         {
            message.Attachments.AddFileAttachment(fileAttach);
         }

         // Override to send to a specific address if configured
         if ((customer == DealerGroupName.Vindis && ConfigService.VindisSendReportsOnlyToMe) ||
             (customer == DealerGroupName.RRGUK && ConfigService.RRGSendReportsOnlyToMe))
         {
            message.ToRecipients.Clear();
            message.ToRecipients.Add("<EMAIL>");
         }

         try
         {
            // Directly send the message and save a copy to the Sent Items folder
            await message.SendAndSaveCopy();
            logger.Info($"Message sent successfully to {string.Join(", ", message.ToRecipients)}");
         }
         catch (Exception ex)
         {
            logger.Error($"Failed sending message to {message.ToRecipients.FirstOrDefault()}: {ex.Message}");
         }
      }




      public async static System.Threading.Tasks.Task SendMail(DealerGroupName customer, string subjectText, string messageText, string fileAttach, ILog logger, List<string> tos = null, List<string> ccs = null)
      {
         var service = await ProvideExchangeService();

         EmailMessage message = new EmailMessage(service);
         message.Body = new MessageBody(BodyType.HTML, messageText); ;
         message.Subject = subjectText;

         if (tos != null)
         {
            message.ToRecipients.AddRange(tos);

         }
         else
         {
            message.ToRecipients.Add("<EMAIL>");
         }

         if (ccs != null)
         {
            message.CcRecipients.AddRange(ccs);
         }


         if (ConfigService.ServiceName.Contains("Dev")) { message.Subject += "_Dev"; }

         if (fileAttach != null)
         {
            message.Attachments.AddFileAttachment(fileAttach);
         }



         //override to send to me
         if ((customer == DealerGroupName.Vindis && ConfigService.VindisSendReportsOnlyToMe) || (customer == DealerGroupName.RRGUK && ConfigService.RRGSendReportsOnlyToMe))
         {
            message.ToRecipients.Clear();
            message.ToRecipients.Add("<EMAIL>");
            //message.ToRecipients.Add("<EMAIL>");
            //message.ToRecipients.Add("<EMAIL>");
            //message.CcRecipients.Clear();
         }

         //temp

         try
         {

            // Save the message to the Drafts folder
            await message.Save(WellKnownFolderName.Drafts);

            // Define the properties to load (including InternetMessageId)
            PropertySet props = new PropertySet(BasePropertySet.IdOnly, EmailMessageSchema.InternetMessageId);

            // Reload the message with the specified properties
            await message.Load(props);

            // Wait for x seconds
            try
            {
               await SendSavedMessage(service, message, 1000);

            }
            catch (Exception ex)
            {
               try
               {
                  logger.Error($"Failed sending message on 1st attempt, trying again: {ex.Message}");
                  await SendSavedMessage(service, message, 5000);
                  logger.Info("Messaged succeeded on first retry");
               }
               catch (Exception ex1)
               {
                  logger.Error($"Failed sending message on 2nd attempt, trying again: {ex.Message}");
                  await SendSavedMessage(service, message, 5000);
                  logger.Info("Messaged succeeded on second retry");
               }
            }

         }
         catch (Exception ex)
         {
            logger.Error($"Failed sending message to {message.ToRecipients.First()}");
            { }
         }


         return;

      }

      private static async System.Threading.Tasks.Task SendSavedMessage(ExchangeService service, EmailMessage message, int delay)
      {
         await System.Threading.Tasks.Task.Delay(delay);

         // Bind to the Drafts folder
         Folder draftsFolder = await Folder.Bind(service, WellKnownFolderName.Drafts);
         ItemView view = new ItemView(1); // Look for the most recent item, adjust as necessary

         // Create the search filter using the loaded InternetMessageId
         SearchFilter searchFilter = new SearchFilter.IsEqualTo(EmailMessageSchema.InternetMessageId, message.InternetMessageId);

         // Find the item in the Drafts folder
         FindItemsResults<Item> findResults = await service.FindItems(draftsFolder.Id, searchFilter, view);

         if (findResults.TotalCount > 0)
         {
            // Assuming the first found item is the email to be sent
            EmailMessage emailToBeSent = findResults.Items[0] as EmailMessage;
            await emailToBeSent.Load(new PropertySet(EmailMessageSchema.InternetMessageId));
            await emailToBeSent.SendAndSaveCopy();
         }
         else
         {
            Console.WriteLine("No email found with the specified InternetMessageId.");
         }
      }

      public static async Task<ExchangeService> ProvideExchangeService()
      {
         ///SPK-2789 migrated to new approach
         var cca = ConfidentialClientApplicationBuilder
             .Create(ConfigService.MailAppId)
             .WithClientSecret(ConfigService.MailSecretValue)
             .WithTenantId(ConfigService.MailAppTenantId)
             .Build();
         var ewsScopes = new string[] { "https://outlook.office365.com/.default" };

         AuthenticationResult authResult = null;
         try
         {
            authResult = await cca.AcquireTokenForClient(ewsScopes).ExecuteAsync();
         }
         catch (Exception ex)
         {
            await System.Threading.Tasks.Task.Delay(3000); //3 sec delay
            authResult = await cca.AcquireTokenForClient(ewsScopes).ExecuteAsync();
         }

         var ewsClient = new ExchangeService(ExchangeVersion.Exchange2015);
         ewsClient.Url = new Uri("https://outlook.office365.com/EWS/Exchange.asmx");
         ewsClient.Credentials = new OAuthCredentials(authResult.AccessToken);
         ewsClient.ImpersonatedUserId = new ImpersonatedUserId(ConnectingIdType.SmtpAddress, ConfigService.MailUser);

         return ewsClient;
      }


      public static string GiveEmojiNumber(int numberChar)
      {
         Dictionary<int, string> lookup = new Dictionary<int, string>
            {
                {0, "0️⃣"},
                {1, "1️⃣"},
                {2, "2️⃣"},
                {3, "3️⃣"},
                {4, "4️⃣"},
                {5, "5️⃣"},
                {6, "6️⃣"},
                {7, "7️⃣"},
                {8, "8️⃣"},
                {9, "9️⃣"},
                {10, "🔟"},

            };

         string result = lookup[numberChar];
         if (result == null)
         {
            return numberChar.ToString();
         }
         else
         {
            return result;
         }
      }



      public static async System.Threading.Tasks.Task LogException(Exception ex, ILog logger, string jobName)
      {
         // Log the full error message and stack trace
         logger.Error($"An error occurred: {ex.Message}\nStackTrace: {ex.StackTrace}");
         // Optionally, if the exception has an inner exception, log that as well
         if (ex.InnerException != null)
         {
            logger.Error($"Inner exception: {ex.InnerException.Message}\nStackTrace: {ex.InnerException.StackTrace}");
         }
         await EmailerService.SendMailOnError(null, $"{jobName} - Error", ex);
      }

      private static async System.Threading.Tasks.Task SaveAndSendFromDraft(ExchangeService service, EmailMessage message)
      {
         await message.Save(WellKnownFolderName.Drafts);



         // Bind to the Drafts folder
         FolderId draftsFolderId = new FolderId(WellKnownFolderName.Drafts);

         // Define a search filter for the subject containing "your_mailsubject_selection_text"
         SearchFilter searchFilter = new SearchFilter.ContainsSubstring(ItemSchema.Subject, message.Subject);

         // Define the item view to specify the number of items to retrieve
         ItemView view = new ItemView(5); // Adjust the number if needed

         // Find items in the Drafts folder matching the search filter
         FindItemsResults<Item> draftItems = await service.FindItems(draftsFolderId, searchFilter, view);

         foreach (Item item in draftItems.Items)
         {
            if (item is EmailMessage draftEmail)
            {
               // Send the email
               await draftEmail.SendAndSaveCopy();
            }
         }

      }


   }
}
