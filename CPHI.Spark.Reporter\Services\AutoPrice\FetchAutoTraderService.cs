﻿using CPHI.Spark.BusinessLogic.AutoPrice;
using CPHI.Spark.DataAccess;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.DataAccess.DataAccess;
using CPHI.Spark.Model;
using CPHI.Spark.Model.AutoPrice;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Model.ViewModels.AutoPricing.RawDataTypes;
using CPHI.Spark.Repository;
using Datadog.Trace;
using log4net;
using StockPulse.WebApi.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace CPHI.Spark.Reporter.Services.AutoPrice
{
   public class FetchAutoTraderService
   {

      private readonly HttpClient httpClient;
      private readonly IHttpClientFactory _httpClientFactory;
      private RallyeApiTokenClient rallyeTokenClient;

      public FetchAutoTraderService(IHttpClientFactory httpClientFactory)
      {
         this.httpClient = httpClientFactory.CreateClient();
         this._httpClientFactory = httpClientFactory;
      }

      public async Task<EnterpriseStockLoadStatus> CheckIfEnterpriseStockLoaded(ILog logger, Model.DealerGroupName dealerGroup)
      {
         var logMessagesDataAccess = new LogMessagesDataAccess(ConfigService.GetConnectionString(dealerGroup));
         return await logMessagesDataAccess.GetEnterpriseStockJobStatus();
      }


      public async Task FetchAutoTraderAdverts(ILog logger, List<Model.DealerGroupName> dealerGroups)
      {
         if (dealerGroups.Count == 0) { return; }
         try
         {
            logger.Info("----------------------------------------------------------");
            logger.Info("FetchAdverts");

            using (var parentScope = Tracer.Instance.StartActive("FetchAdverts"))
            {
               HttpClient httpClient = HttpClientFactoryService.HttpClientFactory.CreateClient();

               //loop through every customer to see if they have retailer sites, if so, get their prices
               foreach (Model.DealerGroupName dealerGroup in dealerGroups)
               {
                  using (var childScope = Tracer.Instance.StartActive($"Begin dealergroup {dealerGroup}"))
                  {
                     childScope.Span.SetTag("DealerGroup", dealerGroup.ToString());

                     var logMessage = LoggingService.InitLogMessage();
                     try
                     {
                        logger.Info($"FetchAdverts: {dealerGroup}");

                        RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(ConfigService.GetConnectionString(dealerGroup));
                        List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);

                        if (retailers.Count > 0)
                        {
                           try
                           {
                              await CollectAdvertsForRetailers(logger, dealerGroup, retailers);
                           }
                           catch (Exception ex)
                           {
                              logger.Error(ex);
                              await EmailerService.SendMailOnError(dealerGroup, "FetchAdverts - Error", ex);
                              LoggingService.AddErrorLogMessage(logMessage, ex.Message);
                           }
                        }
                        else
                        {
                           logger.Info($"FetchAdverts: {dealerGroup}: No retailers found");
                        }
                     }
                     catch (Exception ex)
                     {
                        await EmailerService.LogException(ex, logger, "FetchAdverts");
                        LoggingService.AddErrorLogMessage(logMessage, ex.Message);
                     }
                     finally
                     {
                        await LoggingService.FinalizeAndSaveLogMessage(dealerGroup, logMessage, "FetchAdverts");
                     }
                  }
               }

               logger.Info("FetchAdverts: Completed");
               logger.Info("----------------------------------------------------------");
            }
         }
         catch (Exception e)
         {
            logger.Error(e);
            throw new Exception(e.Message, e);
         }
      }




      public async Task MeasureOtherSiteRetailRatings(ILog logger, List<Model.DealerGroupName> dealerGroups)
      {
         if (dealerGroups.Count == 0) { return; }
         try
         {
            logger.Info("----------------------------------------------------------");
            logger.Info("LocationOptimiser:MeasureRatings");

            using (var parentScope = Tracer.Instance.StartActive("LocationOptimiser:MeasureRatings"))
            {
               //loop through every customer to see if they have retailer sites, if so, get their prices
               foreach (Model.DealerGroupName dealerGroup in dealerGroups)
               {
                  using (var childScope = Tracer.Instance.StartActive($"Begin dealergroup {dealerGroup}"))
                  {
                     childScope.Span.SetTag("DealerGroup", dealerGroup.ToString());

                     logger.Info($"LocationOptimiser:MeasureRatings: {dealerGroup}");
                     var logMessage = LoggingService.InitLogMessage();

                     RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(ConfigService.GetConnectionString(dealerGroup));
                     List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);

                     if (retailers.Count > 0)
                     {
                        try
                        {
                           await MeasureOtherSiteRatingsThisDealerGroup(logger, dealerGroup);
                           //if (dealerGroup == DealerGroupName.Enterprise)
                           //{
                           //    await MeasureOtherSiteRatingsThisDealerGroupUsingLongLatApproach(logger, dealerGroup);
                           //}
                           //else
                           //{
                           //}
                        }
                        catch (Exception ex)
                        {
                           logger.Error(ex);
                           await EmailerService.SendMailOnError(dealerGroup, "LocationOptimiser:MeasureRatings - Error", ex);
                           LoggingService.AddErrorLogMessage(logMessage, ex.Message);
                        }
                        finally
                        {
                           await LoggingService.FinalizeAndSaveLogMessage(dealerGroup, logMessage, "LocationOptimiser:MeasureRatings");
                        }
                     }
                  }
               }

               logger.Info("Completed LocationOptimiser:MeasureRatings");
               //logger.Info("----------------------------------------------------------");
            }
         }
         catch (Exception e)
         {
            logger.Error(e);
            throw new Exception(e.Message, e);
         }
      }




      public VehicleMetricParams CreateParamforVehicleMetricApi(VehicleAdvertWithRetailRating activeAdvert, List<RetailerSite> retailers, bool stripOutCurrentAdSite, bool useLongLats)
      {
         List<VehicleMetricLocationParams> locations;
         List<RetailerSite> relevantRetailers = stripOutCurrentAdSite ? retailers.Where(r => r.RetailerId != activeAdvert.RetailerIdentifier).ToList() : retailers;

         locations = relevantRetailers.Select(x => new VehicleMetricLocationParams(x, useLongLats)).ToList();

         return new VehicleMetricParams()
         {
            vehicleAdvertSnapshotId = activeAdvert.VehicleAdvertSnapshotId,
            advertiserId = RetailerIdSwapService.ProvideUpdatedId(activeAdvert.RetailerIdentifier.ToString()),

            derivativeId = activeAdvert.Derivative,
            firstRegistrationDate = activeAdvert.FirstRegisteredDate?.ToString("yyyy-MM-dd"),
            odometerReadingMiles = activeAdvert.OdometerReading,
            locations = locations
         };
      }



      private async Task MeasureOtherSiteRatingsThisDealerGroup(ILog logger, DealerGroupName dealerGroup)
      {
         //----------------------------------------
         // 1. Gather sites and adverts
         //----------------------------------------
         //Retails sites
         RetailerSitesDataAccess ret = new RetailerSitesDataAccess(ConfigService.GetConnectionString(dealerGroup));
         List<RetailerSite> retailers = await ret.GetRetailerSites(dealerGroup);

         //Adverts
         VehicleAdvertsDataAccess vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(ConfigService.GetConnectionString(dealerGroup));
         IEnumerable<VehicleAdvertWithRetailRating> activeAdverts = await vehicleAdvertsDataAccess.GetActiveAdvertsHavingRetailRating(dealerGroup);


         //----------------------------------------
         // 2. Build params and use to get Responses
         //----------------------------------------
         List<VehicleMetricParams> vehicleMetricAPIParams = BuildUpParams(dealerGroup, retailers, activeAdverts);
         List<VehicleMetricProcessedResponse> vehicleMetricAPIResponses = await DownloadAdvertsWithRetailRatingAtMultipleLocationsNEW(vehicleMetricAPIParams, logger);
         logger.Info($"FetchOtherSiteRetailRatings: {dealerGroup}: Downloaded ratings for other sites for AT request");


         //----------------------------------------
         // 3. Turn into ratings by site
         //----------------------------------------
         List<VehicleAdvertRatingBySite> vehicleAdvertRatingBySites = BuildUpRatingsBySites(logger, retailers, vehicleMetricAPIResponses);
         await SaveItemsInDb(dealerGroup, vehicleAdvertRatingBySites);
      }





      private List<VehicleMetricParams> BuildUpParams(DealerGroupName dealerGroup, List<RetailerSite> retailers, IEnumerable<VehicleAdvertWithRetailRating> activeAdverts)
      {
         List<VehicleMetricParams> vehicleMetricAPIParams = new List<VehicleMetricParams>();
         bool stripOutCurrentSite = dealerGroup != DealerGroupName.Enterprise;
         foreach (var activeAdvert in activeAdverts)
         {

            if (activeAdvert.Derivative == null || activeAdvert.Make == string.Empty || activeAdvert.Make == null || !activeAdvert.FirstRegisteredDate.HasValue)
            {
               continue;
            }

            List<RetailerSite> retailersFilteredForMakes = retailers.Where(x => dealerGroup == DealerGroupName.Enterprise || //we always do all locations for Enterprise
                    x.Makes.ToUpper().Split(',').Contains(activeAdvert.Make.ToUpper()))
                .ToList();


            //must have at least one other site, else there is no point
            if (retailersFilteredForMakes.Any(r => r.Id != activeAdvert.RetailerSitesId))
            {
               bool useLongLats = dealerGroup == DealerGroupName.Enterprise;
               vehicleMetricAPIParams.Add(CreateParamforVehicleMetricApi(activeAdvert,
                retailersFilteredForMakes, stripOutCurrentSite, useLongLats));
            }
         }

         return vehicleMetricAPIParams;
      }



      private static async Task SaveItemsInDb(DealerGroupName dealerGroup, List<VehicleAdvertRatingBySite> vehicleAdvertRatingBySites)
      {
         VehicleAdvertRatingBySitesDataAccess vars = new VehicleAdvertRatingBySitesDataAccess(ConfigService.GetConnectionString(dealerGroup));
         await vars.AddAll(vehicleAdvertRatingBySites);
      }

      private static List<VehicleAdvertRatingBySite> BuildUpRatingsBySites(ILog logger, List<RetailerSite> retailers, List<VehicleMetricProcessedResponse> vehicleMetricAPIResponses)
      {
         List<VehicleAdvertRatingBySite> vehicleAdvertRatingBySites = new List<VehicleAdvertRatingBySite>();

         foreach (var response in vehicleMetricAPIResponses)
         {

            try
            {
               if (response.locations != null)
               {
                  foreach (var location in response.locations)
                  {
                     decimal strategyPrice = 0;
                     vehicleAdvertRatingBySites.Add(new VehicleAdvertRatingBySite(location, response.vehicleAdvertSnapshotId));
                  }
               }

            }
            catch (Exception e)
            {
               logger.Error($"Failed on snapshot #{response.vehicleAdvertSnapshotId}.   Error: {e.Message}");
            }
         }

         return vehicleAdvertRatingBySites;
      }


      private async Task CollectAdvertsForRetailers(ILog logger, Model.DealerGroupName dealerGroup, List<RetailerSite> retailers)
      {
         /// ------------------------------------------------------------------------------------------------------------
         /// Here we get the responses from AutoTrader for each site.   They are crudely parsed into a temporary type.
         /// ------------------------------------------------------------------------------------------------------------

         if(dealerGroup == DealerGroupName.RRGUK)
         {
            rallyeTokenClient = new RallyeApiTokenClient(HttpClientFactoryService.HttpClientFactory, ConfigService.RallyeApiKey, ConfigService.RallyeApiSecret, ConfigService.RallyeBaseURL);
            var tokenResponse = (await rallyeTokenClient.GetToken());

            try
            {
               // This was purely for testing 
               // var response = await rallyeTokenClient.SendTestContainersAsync();
            }
            catch (Exception e) 
            {
               { }
            }
            
         }


         //new - tag any existing ads as no longer the latest
         VehicleAdvertsDataAccess vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(ConfigService.GetConnectionString(dealerGroup));
         VehicleAdvertSnapshotsDataAccess vehicleAdvertSnapshotsDataAccess = new VehicleAdvertSnapshotsDataAccess(ConfigService.GetConnectionString(dealerGroup));

         await vehicleAdvertsDataAccess.SetAllDealerGroupSnapshotsToNoLongerLatest((int)dealerGroup);


         //only go get ads if we are not Enterprise
         if (dealerGroup == DealerGroupName.Enterprise)
         {
            return;
         }

         List<VehicleListing> resultsBySite = await DownloadIncomingVehiclesFromAutoTrader(retailers, logger);
         List<AutoTraderVehicleListing> allAdverts = new List<AutoTraderVehicleListing>();
         foreach (var res in resultsBySite)
         {
            if (res.errorMessage != string.Empty)
            {
               logger.Error(res.errorMessage);
            }
            else
            {
               logger.Info($"FetchAutoTraderPrices: {dealerGroup}: Got {res.results.Count} adverts for {res.RetailerName}");
               allAdverts.AddRange(res.results);
            }
         }

         logger.Info($"FetchAutoTraderPrices: {dealerGroup}: Got {allAdverts.Count} autotrader listing(s)"); //of which {{allPublishedAdverts.Count}} published

         try
         {
            DateTime snapshotDate = DateTime.UtcNow;
            Dictionary<string, LatestChassisAndStockNumber> chassisAndStocknumberDict = GetLatestChassisAndStockNumberLookup(ConfigService.GetConnectionString(dealerGroup), dealerGroup);
            Dictionary<int, RetailerSite> sitesDictionary = retailers.ToDictionary(x => x.RetailerId);

            //save incoming vehicle information
            List<VehicleAdvert> allIncomingAds = ExtractIncomingVehiclesList(allAdverts, chassisAndStocknumberDict, sitesDictionary, snapshotDate, logger);

            foreach (var incomingAd in allIncomingAds)
            {
               if (incomingAd.VehicleReg != null && incomingAd.VehicleReg.Length > 7)
               {
                  logger.Error($"Truncating {incomingAd.VehicleReg} as is over 7 char for REG.");
                  incomingAd.VehicleReg = incomingAd.VehicleReg.Substring(0, 7);
               }
               if (incomingAd.Chassis != null && incomingAd.Chassis.Length > 20)
               {
                  logger.Error($"Truncating {incomingAd.Chassis} as is over 20 char for Chassis.");
                  incomingAd.Chassis = incomingAd.Chassis.Substring(0, 20);
               }

            }

            var result = await rallyeTokenClient.SendVehicleAdvertsAsync(allIncomingAds);

            //if (dealerGroup == DealerGroupName.RRGUK)
            //{
            //   // Group by site, ignore null/blank regs
            //   var groups = allIncomingAds
            //       .Where(ad => ad?.VehicleReg != null)
            //       .GroupBy(ad => ad.RetailerSite_Id);

            //   foreach (var siteGroup in groups)
            //   {
            //      var adsForSite = siteGroup.ToList();

            //      foreach (var chunk in adsForSite.Chunk(5)) //
            //      {
            //         try
            //         {
            //            var result = await rallyeTokenClient.SendVehicleAdvertsAsync(chunk.ToList());
            //         }
            //         catch (Exception ex)
            //         {
            //            { }
            //         }
            //      }
            //   }
            //}


            var allIncomingVehicles = await vehicleAdvertsDataAccess.UpsertVehicles(allIncomingAds, dealerGroup);
            var allIncomingVehiclesLookup = allIncomingVehicles.GroupBy(x => x.WebSiteStockIdentifier).Select(x => x.First()).ToDictionary(x => x.WebSiteStockIdentifier);
            logger.Info($"FetchAutoTraderPrices: {dealerGroup}: Saved incoming vehicle ads");

            //save incoming valuation information and other metrics
            List<VehicleAdvertSnapshot> vehicleAdvertSnapshots = ExtractVehicleSnapshotInformation(
               allAdverts, allIncomingVehiclesLookup, snapshotDate, dealerGroup);

            await vehicleAdvertSnapshotsDataAccess.SaveNewSnapshots(vehicleAdvertSnapshots);

            logger.Info($"FetchAutoTraderPrices: {dealerGroup}: Saved incoming vehicle snapshots");

            //Save features
            //Upsert all the  features received
            List<PortalOption> portalOptions = new List<PortalOption>();

            var allFeatures = allAdverts.SelectMany(a => a.features); //.Where(f => f.featureType == "Optional

            foreach (var feature in allFeatures)
            {
               portalOptions.Add(new PortalOption() { OptionName = feature.name });
            }
            var distinctOptions = portalOptions.DistinctBy(x => x.OptionName).ToList();
            var savedPortalOptions = await vehicleAdvertsDataAccess.UpsertPortalOptions(distinctOptions, dealerGroup);

            //Save vehicle features mapping
            await vehicleAdvertsDataAccess.UpsertVehicleAdvertPortalOptions(allAdverts, allIncomingVehicles, dealerGroup);
         }


         catch (Exception e)
         {
            logger.Error(e);
            throw;
         }
      }



      private static List<VehicleAdvertSnapshot> ExtractVehicleSnapshotInformation(List<AutoTraderVehicleListing> adverts,
      Dictionary<string, StockIdentifierAndId> savedVehicleLookup, DateTime snapshotDate,
      DealerGroupName dealerGroup)
      {
         List<VehicleAdvertSnapshot> vehicleAdvertSnapshots = new List<VehicleAdvertSnapshot>();
         foreach (var vehicle in adverts)
         {
            int vehicleId = savedVehicleLookup[vehicle.metadata.stockId].VehicleId;
            vehicleAdvertSnapshots.Add(new VehicleAdvertSnapshot(vehicle, vehicleId, snapshotDate, (int)dealerGroup));
         }

         return vehicleAdvertSnapshots;
      }



      private async Task<List<VehicleListing>> DownloadIncomingVehiclesFromAutoTrader(List<RetailerSite> sites, ILog? logger)
      {
         var atStockClient = new AutoTraderStockClient(_httpClientFactory, ConfigService.AutotraderApiKey,
         ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
         List<VehicleListing> vehicles = await atStockClient.GetAllStockDetailsForSites(
            ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret,
            sites, ConfigService.AutotraderBaseURL, logger);
         return vehicles;
      }



      private static Dictionary<string, LatestChassisAndStockNumber> GetLatestChassisAndStockNumberLookup(string connString, DealerGroupName dealerGroup)
      {
         StockDataAccess stockDataAccess = new StockDataAccess(connString);
         var chassisAndStocknumberDict = stockDataAccess.GetMostRecentChassisAndStockNumberFull(dealerGroup);
         return chassisAndStocknumberDict;
      }

      private static List<VehicleAdvert> ExtractIncomingVehiclesList(List<AutoTraderVehicleListing> adverts,
                                                                     Dictionary<string, LatestChassisAndStockNumber> chassisAndStocknumberDict,
                                                                     Dictionary<int, RetailerSite> sitesDictionary,
                                                                     DateTime snapshotDate, ILog logger)
      {
         List<VehicleAdvert> incomingVehicles = new List<VehicleAdvert>();
         foreach (var vehicle in adverts)
         {
            if (vehicle.vehicle.registration == "MM67ZXK")
            {
               { }
            }
            string stockNumberFull = "Unknown";
            LatestChassisAndStockNumber stockNumberMatch = null;
            bool didFindInDictionary = vehicle.vehicle?.vin != null && chassisAndStocknumberDict.TryGetValue(vehicle.vehicle.vin, out stockNumberMatch);
            if (didFindInDictionary) { stockNumberFull = stockNumberMatch?.StockNumberFull; }

            int retailerId = int.Parse(vehicle.advertiser.advertiserId);
            try
            {
               incomingVehicles.Add(new VehicleAdvert(vehicle, stockNumberFull, sitesDictionary[retailerId].Id, snapshotDate));
            }
            catch (Exception ex)
            {
               logger.Error($"Failed during ExtractIncomingVehiclesList for {vehicle.vehicle.registration} - {ex.Message}");
               continue;
            }
         }

         return incomingVehicles;
      }

      private async Task<List<VehicleMetricProcessedResponse>> DownloadAdvertsWithRetailRatingAtMultipleLocationsNEW(List<VehicleMetricParams> vehicleMetricAPIParams, ILog logger)
      {
         // HttpClient httpClient = new HttpClient();
         AutoTraderVehicleMetricsClient atMetricsClient = new AutoTraderVehicleMetricsClient(
            _httpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret,
            ConfigService.AutotraderBaseURL);
         var atTokenClient = new AutoTraderApiTokenClient(_httpClientFactory, ConfigService.AutotraderApiKey,
          ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
         TokenResponse _bearerToken = (await atTokenClient.GetToken());

         List<VehicleMetricProcessedResponse> vehicles = await
         atMetricsClient.GetAdvertsWithRetailRatingAtMultipleLocationsNEW(vehicleMetricAPIParams,
         ConfigService.AutotraderBaseURL, _bearerToken);
         foreach (var vehicle in vehicles)
         {
            if (vehicle.errorMessage != null && vehicle.errorMessage != string.Empty)
            {
               logger.Error(vehicle.errorMessage);
            }
         }
         return vehicles;
      }



   }
}
