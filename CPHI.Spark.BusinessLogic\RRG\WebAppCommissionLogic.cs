﻿using CPHI.Spark.BusinessLogic.Vindis;
using CPHI.Spark.Model.ViewModels.RRG;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CPHI.Spark.BusinessLogic.RRG
{
    public  class WebAppCommissionLogic
    {
        

        private string _connectionString;

        public WebAppCommissionLogic(string connString)
        {
            _connectionString = connString;
        }

        //---------------------------------------------------------------x
        // used by web app.  Simpler as just provides CommissionStatement
        // with prior month lines merged to one line per month rather than
        // a whole collection of detailed lines for each month
        //---------------------------------------------------------------x

        ///if most recent payout calc is last month (vs the month we're reporting on) then should include prior month adjustments to this month's results
        public async Task<IEnumerable<CommissionStatement>> CalcCommissions(DateTime monthToCalcFor, Model.DealerGroupName dealerGroup)
        {
            CommissionGetDataService commissionGetDataService = new CommissionGetDataService(_connectionString);
            DateTime mostRecentPaidOutMonth = commissionGetDataService.GetMostRecentPaidMonth(dealerGroup);
            bool includePriorMonthAdjustments = monthToCalcFor.AddMonths(-1) <= mostRecentPaidOutMonth;

            if (includePriorMonthAdjustments)
            {
                return await CalcCommissionWithAdjustments(monthToCalcFor.Date, mostRecentPaidOutMonth);
            }
            else
            {
                return await CalcCommissionWithoutAdjustments(monthToCalcFor.Date);
            }
        }


        private async Task<IEnumerable<CommissionStatement>> CalcCommissionWithoutAdjustments(DateTime monthToCalcFor)
        {
            CommissionCoster _coster = new CommissionCoster();

            Dictionary<string, decimal> latestViewOfOrderEarnRates = new Dictionary<string, decimal>();
            for (int i = -12; i <= -1; i++)
            {
                var thisMonthItems = await _coster.GetCostedCommissionsForMonth(monthToCalcFor.AddMonths(i), monthToCalcFor, _connectionString, latestViewOfOrderEarnRates);
                foreach (var item in thisMonthItems)
                {
                    if (item.OnlyPayOrderMoney)
                    {
                        latestViewOfOrderEarnRates.Add($"{item.StockNumber}|{item.SalesmanId}", item.PayRateVehicle);
                    }
                }
            }


            ILookup<int, CommissionItem> uniqSalesExecsLineSets = (await  _coster.GetCostedCommissionsForMonth(monthToCalcFor, monthToCalcFor, _connectionString, latestViewOfOrderEarnRates)).ToLookup(x => x.SalesmanId);
            List<CommissionStatement> results = new List<CommissionStatement>();
            foreach (var execWithLines in uniqSalesExecsLineSets)
            {
                results.Add(new CommissionStatement()
                {
                    Items = execWithLines.ToList(),
                    SalesmanId = execWithLines.Key,
                    SiteId = execWithLines.ToList()[0].SalesmanSiteId
                });
            }
            return results;
        }



        private async Task<IEnumerable<CommissionStatement>> CalcCommissionWithAdjustments(DateTime monthToCalcFor, DateTime latestPaidMonth)
        {
            CommissionCoster _coster = new CommissionCoster();
            CommissionGetDataService commissionGetDataService = new CommissionGetDataService(_connectionString);
            

            //populate previous 12m recosted lists and actually paid out lookup lists by salesman
            List<ILookup<int, CommissionItem>> costedItemsLookupLists = new List<ILookup<int, CommissionItem>>();
            List<ILookup<int, CommissionItem>> paidOutItemsLookupLists = new List<ILookup<int, CommissionItem>>();
            Dictionary<string, decimal> latestViewOfOrderEarnRates = new Dictionary<string, decimal>();
            for (int i = -12; i <= -1; i++)
            {
                var thisMonthItems = await _coster.GetCostedCommissionsForMonth(monthToCalcFor.AddMonths(i), monthToCalcFor, _connectionString,latestViewOfOrderEarnRates);
                foreach (var item in thisMonthItems)
                {
                    if (item.OnlyPayOrderMoney)
                    {
                        latestViewOfOrderEarnRates.Add($"{item.StockNumber}|{item.SalesmanId}", item.PayRateVehicle);
                    }
                }

                costedItemsLookupLists.Add(thisMonthItems.ToLookup(x => x.SalesmanId));
                paidOutItemsLookupLists.Add((await commissionGetDataService.GetActualPayoutsForMonth(monthToCalcFor.AddMonths(i), latestPaidMonth)).ToLookup(x => x.SalesmanId));
            }

            ILookup<int, CommissionItem> uniqSalesExecsLineSets = (await _coster.GetCostedCommissionsForMonth(monthToCalcFor, monthToCalcFor, _connectionString, latestViewOfOrderEarnRates)).ToLookup(x => x.SalesmanId);

            List<CommissionStatement> results = new List<CommissionStatement>();
            //Iterate over each salesman and populate results
            foreach (var execWithLines in uniqSalesExecsLineSets)
            {

                CommissionItem adjustmentForPriorMonths = new CommissionItem();
                decimal adjustmentValue = 0;

                for (int i = 0; i < 12; i++)
                {
                    adjustmentValue += costedItemsLookupLists[i][execWithLines.Key].Select(x => x.TotalCommission).Sum();
                    adjustmentValue -= paidOutItemsLookupLists[i][execWithLines.Key].Select(x => x.TotalCommission).Sum();
                }

                adjustmentForPriorMonths = new CommissionItem()
                {
                    Customer = $"Impact of previous months recalculation",
                    PayRateOther = adjustmentValue
                };


                results.Add(new CommissionStatement()
                {
                    Items = execWithLines.Concat(new List<CommissionItem>() { adjustmentForPriorMonths }).ToList(),
                    SalesmanId = execWithLines.Key,
                    SiteId = execWithLines.ToList()[0].SalesmanSiteId
                });
            }

            return results;
        }

    }
}
