using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace CPHI.Spark.Model.ViewModels
{
    public class Container
    {
        [JsonPropertyName("location")]
        public LocationRef Location { get; set; } = new();

        [JsonPropertyName("container_type")]
        public ContainerTypeRef ContainerType { get; set; } = new();

        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("attributes")]
        public List<ContainerAttribute> Attributes { get; set; } = new();
    }
}
