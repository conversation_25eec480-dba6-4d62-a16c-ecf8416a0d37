# ASP.NET Core (.NET Framework)
# Build and test ASP.NET Core projects targeting the full .NET Framework.
# Add steps that publish symbols, save build artifacts, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/dotnet-core

trigger:
  tags:
    include:
      - webappStage_*

pool:
  default

variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'

jobs:
- job: BuildAndPublishSpark
  displayName: Build And Publish Spark

  steps:
  - task: NuGetToolInstaller@1
    displayName: 'Install NuGet'

  # Authenticate with Azure Artifacts to access your private feed.
  - task: NuGetAuthenticate@0
    displayName: 'Authenticate with Azure Artifacts'

  # Restore NuGet packages using the NuGet.config that includes CPHI_Feed.
  - task: NuGetCommand@2
    displayName: 'Restore NuGet Packages'
    inputs:
      restoreSolution: '$(solution)'
      feedsToUse: 'config'
      nugetConfigPath: 'NuGet.config'  # Update path if your file is elsewhere.

  # Build API Project
  - task: DotNetCoreCLI@2
    displayName: 'Build API Project'
    inputs:
      command: 'build'
      arguments: '-c Release -r=linux-x64'
      projects: '**/CPHI.Spark.WebApp.csproj'

  # Publish API Project
  - task: DotNetCoreCLI@2
    displayName: 'Publish API Project'
    inputs:
      command: 'publish'
      publishWebProjects: true
      projects: '**/CPHI.Spark.WebApp.csproj'
      zipAfterPublish: true
      arguments: '-o $(Build.ArtifactStagingDirectory)/WebApp/ --runtime=linux-x64 -c Release --no-build'

  # Publish the artifact.
  - task: PublishPipelineArtifact@1
    displayName: 'Publish Spark API Artifact'
    inputs:
      targetPath: '$(Build.ArtifactStagingDirectory)/WebApp/'
      artifactName: 'SparkApi'
