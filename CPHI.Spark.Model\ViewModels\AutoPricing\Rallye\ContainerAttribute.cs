using System.Text.Json.Serialization;

namespace CPHI.Spark.Model.ViewModels
{
    public partial class ContainerAttribute
    {
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("value")]
        public string Value { get; set; } = string.Empty;

        [JsonPropertyName("data_type")]
        public string DataType { get; set; } = string.Empty;

        public ContainerAttribute() { }
        public ContainerAttribute(string name, string value, string dataType)
        {
            Name = name; Value = value; DataType = dataType;
        }
    }
}
