﻿using CPHI.Spark.Model.ViewModels.RRG;
using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace CPHI.Spark.BusinessLogic.RRG
{
    public class ReporterCommissionLogic
    {

        private string _connectionString;

        public ReporterCommissionLogic(string connString)
        {
            _connectionString = connString;
        }




        //---------------------------------------------------------------x
        // used by the reporter.  more complex as provides the full payout 
        // and previously paid line item detail for previous months.
        //-----------------------------------------------------------------

        public async Task<CommissionItemsCollection> GetFullStatementWithAdjustments(DateTime monthToCalcFor, DateTime latestPaidMonth, ILog logger)
        {
            CommissionCoster _coster = new CommissionCoster(logger);
           
            CommissionItemsCollection result = new CommissionItemsCollection();
            
            Dictionary<string, decimal> latestViewOfOrderEarnRates = new Dictionary<string, decimal>();
            for (int i = -12; i <= -1; i++)
            {
                DateTime thisMonth = monthToCalcFor.AddMonths(i);
                Tuple<List<CommissionItem>, List<CommissionItem>> monthResult = await CreateCostedCommissionsAndActualPayoutsForMonth(monthToCalcFor, thisMonth, latestPaidMonth,logger, latestViewOfOrderEarnRates);
                var itemsNow = monthResult.Item1;
                foreach (var item in itemsNow)
                {
                    if (item.OnlyPayOrderMoney)
                    {
                        latestViewOfOrderEarnRates.Add($"{item.StockNumber}|{item.SalesmanId}", item.PayRateVehicle);
                    }
                }
                
               //set the items now
                PropertyInfo itemsNowProp = typeof(CommissionItemsCollection).GetProperty($"ItemsNowMonthM{i * -1}");
                itemsNowProp.SetValue(result, monthResult.Item1, null);

                //set the paid out
                PropertyInfo itemsWasProp = typeof(CommissionItemsCollection).GetProperty($"ItemsWasMonthM{i * -1}");
                itemsWasProp.SetValue(result, monthResult.Item2, null);
            }
            List<CommissionItem> costedCommItemsThisMonth = _coster.GetCostedCommissionsForMonth(monthToCalcFor, monthToCalcFor, _connectionString, latestViewOfOrderEarnRates).Result.ToList();
            result.Items = costedCommItemsThisMonth;
            return result;
        }

        public async Task<CommissionItemsCollection> GetFullStatementWithAdjustmentsLBDM(DateTime monthToCalcFor, DateTime latestPaidMonth, ILog logger)
        {
            CommissionCosterLBDM _coster = new CommissionCosterLBDM(logger);
            List<CommissionItem> costedCommItemsThisMonth = _coster.GetCostedCommissionsForMonth(monthToCalcFor, monthToCalcFor, _connectionString).Result.ToList();
            CommissionItemsCollection result = new CommissionItemsCollection();
            result.Items = costedCommItemsThisMonth;
            for (int i = -1; i >= -12; i--)
            {
                DateTime priorMonth = monthToCalcFor.AddMonths(i);

                Tuple<List<CommissionItem>, List<CommissionItem>> monthResult = await CreateCostedCommissionsAndActualPayoutsForMonthLBDM(monthToCalcFor, priorMonth, latestPaidMonth, logger);
                
                if(priorMonth < new DateTime(2024, 7, 1))
                {
                    monthResult = Tuple.Create(new List<CommissionItem>(), new List<CommissionItem>());
                }


                //set the items now
                PropertyInfo itemsNowProp = typeof(CommissionItemsCollection).GetProperty($"ItemsNowMonthM{i * -1}");
                itemsNowProp.SetValue(result, monthResult.Item1, null);

                //set the paid out
                PropertyInfo itemsWasProp = typeof(CommissionItemsCollection).GetProperty($"ItemsWasMonthM{i * -1}");
                itemsWasProp.SetValue(result, monthResult.Item2, null);

            }

            return result;
        }

        public async Task<CommissionItemsCollection> GetFullStatementNoAdjustments(DateTime monthToCalcFor,ILog logger)
        {
            CommissionCoster _coster = new CommissionCoster(logger);

            Dictionary<string, decimal> latestViewOfOrderEarnRates = new Dictionary<string, decimal>();
            for (int i = -12; i <= -1; i++)
            {
                DateTime thisMonth = monthToCalcFor.AddMonths(i);
                List<CommissionItem> costedCommItemsMonth = (await _coster.GetCostedCommissionsForMonth(monthToCalcFor, thisMonth, _connectionString, latestViewOfOrderEarnRates)).ToList();
                foreach (var item in costedCommItemsMonth)
                {
                    if (item.OnlyPayOrderMoney)
                    {
                        latestViewOfOrderEarnRates.Add($"{item.StockNumber}|{item.SalesmanId}", item.PayRateVehicle);
                    }
                }
            }


            List<CommissionItem> costedCommItemsThisMonth = (await _coster.GetCostedCommissionsForMonth(monthToCalcFor, monthToCalcFor, _connectionString, latestViewOfOrderEarnRates)).ToList();
            CommissionItemsCollection result = new CommissionItemsCollection();
            result.Items = costedCommItemsThisMonth;
            for (int i = 1; i <= 12; i++)
            {
                PropertyInfo itemsNowProp = typeof(CommissionItemsCollection).GetProperty($"ItemsNowMonthM{i}");
                itemsNowProp.SetValue(result, new List<CommissionItem>(), null);
                PropertyInfo itemsWasProp = typeof(CommissionItemsCollection).GetProperty($"ItemsWasMonthM{i}");
                itemsWasProp.SetValue(result, new List<CommissionItem>(), null);
            }
            return result;
        }

        public async Task<CommissionItemsCollection> GetFullStatementNoAdjustmentsLBDM(DateTime monthToCalcFor, ILog logger)
        {
            CommissionCosterLBDM _coster = new CommissionCosterLBDM(logger);
            List<CommissionItem> costedCommItemsThisMonth = (await _coster.GetCostedCommissionsForMonth(monthToCalcFor, monthToCalcFor, _connectionString)).ToList();
            CommissionItemsCollection result = new CommissionItemsCollection();
            result.Items = costedCommItemsThisMonth;
            for (int i = 1; i <= 12; i++)
            {
                PropertyInfo itemsNowProp = typeof(CommissionItemsCollection).GetProperty($"ItemsNowMonthM{i}");
                itemsNowProp.SetValue(result, new List<CommissionItem>(), null);
                PropertyInfo itemsWasProp = typeof(CommissionItemsCollection).GetProperty($"ItemsWasMonthM{i}");
                itemsWasProp.SetValue(result, new List<CommissionItem>(), null);
            }
            return result;
        }








        //-------------------------------------------------------------------------------------------
        // Private methods
        //-------------------------------------------------------------------------------------------

        private async Task<Tuple<List<CommissionItem>, List<CommissionItem>>> CreateCostedCommissionsAndActualPayoutsForMonth(DateTime mainMonth, 
            DateTime monthToRecalc, DateTime latestPaidMonth,ILog logger, Dictionary<string, decimal> latestViewOfOrderEarnRates)
        {
            CommissionCoster _coster = new CommissionCoster(logger);
            CommissionGetDataService commissionGetDataService = new CommissionGetDataService(_connectionString);
            List<CommissionItem> costedCommItemsMonth = (await _coster.GetCostedCommissionsForMonth(monthToRecalc, mainMonth, _connectionString,latestViewOfOrderEarnRates)).ToList(); //await _coster.GetCostedCommissionsForMonth(monthToRecalc, mainMonth, _connectionString);

            List<CommissionItem> paidOutForMonth = (await commissionGetDataService.GetActualPayoutsForMonth(monthToRecalc, latestPaidMonth)).ToList();
            paidOutForMonth = paidOutForMonth.Where(x=>x.Role!="Fleet").ToList();

            CommonMethods.tagNewOrUpdatedLines(costedCommItemsMonth, paidOutForMonth);
            CommonMethods.tagGoneOrUpdatedLines(costedCommItemsMonth, paidOutForMonth);
            return Tuple.Create(costedCommItemsMonth, paidOutForMonth);
        }


        private async Task<Tuple<List<CommissionItem>, List<CommissionItem>>> CreateCostedCommissionsAndActualPayoutsForMonthLBDM(DateTime mainMonth,
          DateTime monthToRecalc, DateTime latestPaidMonth, ILog logger)
        {
            CommissionCosterLBDM _coster = new CommissionCosterLBDM(logger);
            CommissionGetDataService commissionGetDataService = new CommissionGetDataService(_connectionString);
            List<CommissionItem> costedCommItemsMonth = (await _coster.GetCostedCommissionsForMonth(monthToRecalc, mainMonth, _connectionString)).ToList();

            List<CommissionItem> paidOutMonth = (await commissionGetDataService.GetActualPayoutsForMonth(monthToRecalc, latestPaidMonth)).ToList();
            paidOutMonth = paidOutMonth.Where(x=>x.Role=="Fleet").ToList();

            CommonMethods.tagNewOrUpdatedLines(costedCommItemsMonth, paidOutMonth);
            CommonMethods.tagGoneOrUpdatedLines(costedCommItemsMonth, paidOutMonth);
            return Tuple.Create(costedCommItemsMonth, paidOutMonth);
        }

    }
}
