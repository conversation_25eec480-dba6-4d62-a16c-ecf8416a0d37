﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Repository;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CPHI.Spark.Reporter
{
   public static class ConfigService
   {

      public static bool AmRunningOnDemandPriceChangeJob { get; set; } = false;

      private static IConfigurationBuilder configurationBuilder;

      //------------------------------------
      // Connection Strings
      //------------------------------------
      public static string RRGUKConnectionString { get; set; }
      public static string VindisConnectionString { get; set; }
      public static string RRGSpainConnectionString { get; set; }
      public static string AutoPriceConnectionString { get; set; }
      public static string SytnerConnectionString { get; set; }




      //------------------------------------
      //Override run now
      //------------------------------------
      public static bool RunSiteExcelsJobVindisNow { get; set; }//one-off
      public static bool RunSalesmanPdfsJobVindisNow { get; set; }//one-off
      public static bool RunSiteExcelsJobRRGNow { get; set; }//one-off
      public static bool RunSalesmanPdfsJobRRGNow { get; set; }//one-off

      public static bool RunUsageJobNow { get; set; }//scheduled
      public static bool RunGardXJobVindisPriorMonthNow { get; set; }//scheduled
      public static bool RunGardXJobVindisCurrentMTDNow { get; set; }//scheduled

      public static bool RunFetchAutoTraderPricingNow { get; set; }//scheduled
      public static bool RunFetchAutoTraderPricingSecondRunNow { get; set; }//scheduled
      public static bool RunUpdateWebsitePricesNow { get; set; }//scheduled
                                                                //public static bool RunUpdateWebsitePrices4PMNow { get; set; }//scheduled
      public static bool RunUpdateWebsitePricesOnDemandNow { get; set; }//scheduled
      public static bool RunVehicleValuationNow { get; set; }//scheduled
      public static bool RunVehicleValuationCheckerNow { get; set; }//scheduled
      public static bool RunLocalBargainNow { get; set; }//scheduled

      public static bool RunDerivativeDownloadNow { get; set; } //one-off
      public static bool RunTemporaryJobNow { get; set; } //one-off
      public static bool RunEMGMantlesStockJobNow { get; set; }
      public static bool RunScheduledReportsJobNow { get; set; }
      public static bool RunGetPrivateAdsJobNow { get; set; }
      public static bool RunGetTradeAdsJobNow { get; set; }



      //------------------------------------
      //App wide settings
      //------------------------------------
      public static string ServiceName { get; set; }
      public static string MailUser { get; set; }
      public static string MailPwd { get; set; }
      public static string MailAppTenantId { get; set; }
      public static string MailAppId { get; set; }
      public static string MailSecretValue { get; set; }
      public static string RebuildURL { get; set; }



      //------------------------------------
      //Vindis Commission Stuff
      //------------------------------------
      public static bool VindisSendReportsOnlyToMe { get; set; }
      public static DateTime VindisMonthToRunFor { get; set; }
      public static bool VindisIncludePriorMonthAdjustments { get; set; }

      //Site reports
      public static bool VindisSiteSummaryDoSites { get; set; }
      public static bool VindisSiteSummaryDoTotal { get; set; }
      public static List<int> VindisSiteSummaryLimitToSiteIds { get; set; }
      public static List<string> VindisSiteSummaryLimitToExecNames { get; set; }

      //Person reports
      public static List<string> VindisSalesmanReportLimitToExecNames { get; set; }
      public static List<int> VindisSalesmanReportLimitToSiteIds { get; set; }



      //------------------------------------
      //RRG Commission Stuff
      //------------------------------------
      public static bool RRGSendReportsOnlyToMe { get; set; }

      public static bool RRGIncludePriorMonthAdjustments { get; set; }
      public static DateTime RRGMonthToRunFor { get; set; }


      //Site reports
      public static bool RRGSiteSummaryDoSites { get; set; }
      public static bool RRGSiteSummaryDoTotal { get; set; }
      public static List<int> RRGSiteSummaryLimitToSiteIds { get; set; }
      public static List<string> RRGSiteSummaryLimitToExecNames { get; set; }


      //Person reports
      public static List<string> RRGSalesmanReportLimitToExecNames { get; set; }
      public static List<int> RRGSalesmanReportLimitToSiteIds { get; set; }

      // RRG LBDM
      public static bool RunLBDMExcelsJobRRGNow { get; set; }
      public static bool RunLBDMPdfsJobRRGNow { get; set; }
      //------------------------------------
      // Usage and Gardx
      //------------------------------------

      //Usage report
      public static List<string> UseageReportTo { get; set; }
      public static List<string> UseageReportCc { get; set; }
      public static List<string> EnterpriseLoadReportTo { get; set; }
      public static string ReportUsageStatsJobCronSchedule { get; set; }


      // GardX
      public static List<string> GardXReportTo { get; set; }
      public static List<string> GardXReportCc { get; set; }
      public static string GardXJobCronSchedulePriorMonth { get; set; }
      public static string GardXJobCronScheduleCurrentMonthToDate { get; set; }


      //------------------------------------
      // AutoPricing
      //------------------------------------

      // Pinewood Mantles 
      public static string PinewoodMantlesUsername { get; set; }
      public static string PinewoodMantlesPassword { get; set; }
      public static string PinewoodMantlesOrgId { get; set; }
      public static string PinewoodMantlesFilePath { get; set; }

      //FetchAutoTraderPricingJob
      public static string LocalBargainJobCronSchedule { get; set; }
      public static string VehicleValuationJobCronSchedule { get; set; }
      public static string VehicleValuationCheckerJobCronSchedule { get; set; }
      public static string EMGMantlesStockJobCronSchedule { get; set; }

      public static string FetchAutoTraderPricingJobCronSchedule { get; set; }
      public static string FetchAutoTraderPricingSecondRunJobCronSchedule { get; set; }
      public static string UpdateAutoTraderPricesJobCronSchedule { get; set; }
      public static string UpdateAutoTraderPrices4PMJobCronSchedule { get; set; }
      public static string updateAutoTraderPricesOnDemandJobCronSchedule { get; set; }
      public static string getPrivateAdsJobCronSchedule { get; set; }
      public static string getTradeAdsJobCronSchedule { get; set; }
      public static string sendScheduledReportsJobCronSchedule { get; set; }

      //AutoTrader client
      public static string AutotraderApiKey { get; set; }
      public static string AutotraderApiSecret { get; set; }
      public static string AutotraderBaseURL { get; set; }
      public static string AutotraderBaseURLForChangingPrices { get; set; }

      //Rallye Digital client
      public static string RallyeApiKey { get; set; }
      public static string RallyeApiSecret { get; set; }
      public static string RallyeBaseURL { get; set; }

      //Groups for the various reporter job elements:

      public static string FetchAdvertsGroups { get; set; }
      public static string FetchAdvertsSecondRunGroups { get; set; }
      public static string CreateOptOutsGroups { get; set; }
      public static string FetchJob_GenerateChangesGroupsWeekday { get; set; }
      public static string FetchJob_EmailChangesGroupsWeekday { get; set; }
      public static string FetchJob_EmailChangesGroupsWeekend { get; set; }
      public static string FetchJob_LocationOptimiserGroups { get; set; }
      public static string FetchJob_LocationOptimiserSecondRunGroups { get; set; }
      public static string InStockNotOnPortalGroups { get; set; }
      public static string InStockNotOnPortalSecondRunGroups { get; set; }
      public static string LocalBargainJob_LocalBargainGroup { get; set; }
      public static string GetPrivateAdsGroup { get; set; }
      public static string GetTradeAdsGroup { get; set; }
      public static string ValuationJob_Groups { get; set; }
      public static string ValuationCheckerJob_Groups { get; set; }
      public static string UpdatePrices_UpdateGroup { get; set; }
      public static string UpdatePrices4PM_UpdateGroup { get; set; }
      public static string UpdatePricesOnDemand_UpdateGroup { get; set; }
      public static string SendScheduledReportsGroup { get; set; }



      public static List<string> ErrorEmailTo { get; set; }
      public static List<string> ValuationErrorEmailTo { get; set; }

      public static int ValuationChunkSize { get; set; }
      public static string DVSATokenURL { get; set; }
      public static string DVSABaseURLForRecallApi { get; set; }
      public static string DVSAApiKey { get; set; }
      public static string DVSAClientId { get; set; }
      public static string DVSAClientSecret { get; set; }
      public static string DVSAScope { get; set; }



      //constructor
      static ConfigService()
      {

         var builder = new ConfigurationBuilder().AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
         configurationBuilder = builder;


         FetchAdvertsGroups = GetString("AppSettings", "FetchJob_FetchGroups");
         FetchAdvertsSecondRunGroups = GetString("AppSettings", "FetchJobSecondRun_FetchGroups");
         CreateOptOutsGroups = GetString("AppSettings", "FetchJob_OptOutGroups");
         FetchJob_GenerateChangesGroupsWeekday = GetString("AppSettings", "FetchJob_GenerateChangesGroupsWeekday");
         FetchJob_EmailChangesGroupsWeekday = GetString("AppSettings", "FetchJob_EmailChangesGroupsWeekday");
         FetchJob_EmailChangesGroupsWeekend = GetString("AppSettings", "FetchJob_EmailChangesGroupsWeekend");
         FetchJob_LocationOptimiserGroups = GetString("AppSettings", "FetchJob_LocationOptimiserGroups");
         FetchJob_LocationOptimiserSecondRunGroups = GetString("AppSettings", "FetchJob_LocationOptimiserSecondRunGroups");
         InStockNotOnPortalGroups = GetString("AppSettings", "FetchJob_InStockNotInATGroups");
         InStockNotOnPortalSecondRunGroups = GetString("AppSettings", "FetchJob_InStockNotInATSecondRunGroups");
         LocalBargainJob_LocalBargainGroup = GetString("AppSettings", "LocalBargainJob_LocalBargainGroup");
         GetPrivateAdsGroup = GetString("AppSettings", "GetPrivateAdsGroup");
         GetTradeAdsGroup = GetString("AppSettings", "GetTradeAdsGroup");
         ValuationJob_Groups = GetString("AppSettings", "ValuationJob_Groups");
         ValuationCheckerJob_Groups = GetString("AppSettings", "ValuationCheckerJob_Groups");
         UpdatePrices_UpdateGroup = GetString("AppSettings", "UpdatePrices_UpdateGroup");
         UpdatePrices4PM_UpdateGroup = GetString("AppSettings", "UpdatePrices4PM_UpdateGroup");
         UpdatePricesOnDemand_UpdateGroup = GetString("AppSettings", "UpdatePricesOnDemand_UpdateGroup");
         SendScheduledReportsGroup = GetString("AppSettings", "SendScheduledReportsGroup");


         //------------------------------------
         //App wide settings
         //------------------------------------
         ServiceName = GetString("AppSettings", "serviceName");
         RRGUKConnectionString = GetString("ConnectionStrings", "RRGUK");
         VindisConnectionString = GetString("ConnectionStrings", "Vindis");
         RRGSpainConnectionString = GetString("ConnectionStrings", "RRGSpain");
         AutoPriceConnectionString = GetString("ConnectionStrings", "AutoPrice");
         SytnerConnectionString = GetString("ConnectionStrings", "Sytner");

         MailUser = GetString("AppSettings", "mailUser");
         MailPwd = GetString("AppSettings", "mailPwd");
         MailAppTenantId = GetString("AppSettings", "mailAppTenantId");
         MailAppId = GetString("AppSettings", "mailAppId");
         MailSecretValue = GetString("AppSettings", "mailSecretValue");
         RebuildURL = GetString("AppSettings", "rebuildUrl");

         //------------------------------------
         //Override run now
         //------------------------------------
         RunSiteExcelsJobVindisNow = GetBool("AppSettings", "runSiteExcelsJobVindisNow");
         RunSalesmanPdfsJobVindisNow = GetBool("AppSettings", "runSalesmanPdfsJobVindisNow");
         RunSiteExcelsJobRRGNow = GetBool("AppSettings", "runSiteExcelsJobRRGNow");
         RunSalesmanPdfsJobRRGNow = GetBool("AppSettings", "runSalesmanPdfsJobRRGNow");

         RunLBDMExcelsJobRRGNow = GetBool("AppSettings", "runLBDMExcelsJobRRGNow");
         RunLBDMPdfsJobRRGNow = GetBool("AppSettings", "runLBDMPdfsJobRRGNow");

         RunUsageJobNow = GetBool("AppSettings", "runUsageJobNow");
         RunGardXJobVindisCurrentMTDNow = GetBool("AppSettings", "runGardXJobVindisCurrentMTDNow");
         RunGardXJobVindisPriorMonthNow = GetBool("AppSettings", "runGardXJobVindisPriorMonthNow");
         RunFetchAutoTraderPricingNow = GetBool("AppSettings", "RunFetchAutoTraderPricingNow");
         RunFetchAutoTraderPricingSecondRunNow = GetBool("AppSettings", "RunFetchAutoTraderPricingSecondRunNow");
         RunUpdateWebsitePricesNow = GetBool("AppSettings", "RunUpdateWebsitePricesNow");
         //RunUpdateWebsitePrices4PMNow = GetBool("AppSettings", "RunUpdateWebsitePrices4PMNow");
         RunUpdateWebsitePricesOnDemandNow = GetBool("AppSettings", "RunUpdateWebsitePricesOnDemandNow");
         RunVehicleValuationNow = GetBool("AppSettings", "RunVehicleValuationNow");
         RunVehicleValuationCheckerNow = GetBool("AppSettings", "RunVehicleValuationCheckerNow");
         RunLocalBargainNow = GetBool("AppSettings", "RunLocalBargainNow");
         RunDerivativeDownloadNow = GetBool("AppSettings", "RunDerivativeDownloadNow");
         RunEMGMantlesStockJobNow = GetBool("AppSettings", "RunEMGMantlesStockJobNow");
         RunGetPrivateAdsJobNow = GetBool("AppSettings", "RunGetPrivateAdsJobNow");
         RunGetTradeAdsJobNow = GetBool("AppSettings", "RunGetTradeAdsJobNow");
         RunTemporaryJobNow = GetBool("AppSettings", "RunTemporaryJobNow");
         RunScheduledReportsJobNow = GetBool("AppSettings", "RunScheduledReportsJobNow");




         //------------------------------------
         //Vindis Commission Stuff
         //------------------------------------
         VindisSendReportsOnlyToMe = GetBool("AppSettings", "vindisSendReportsOnlyToMe");
         VindisMonthToRunFor = GetDate("AppSettings", "vindisMonthToRunFor");
         VindisIncludePriorMonthAdjustments = GetBool("AppSettings", "vindisIncludePriorMonthAdjustments");

         //Site reports

         VindisSiteSummaryDoSites = GetBool("AppSettings", "vindisSiteSummaryDoSites");
         VindisSiteSummaryDoTotal = GetBool("AppSettings", "vindisSiteSummaryDoTotal");
         VindisSiteSummaryLimitToSiteIds = GetIntArray("AppSettings", "vindisSiteSummaryLimitToSiteIds");
         VindisSiteSummaryLimitToExecNames = GetStringArray("AppSettings", "vindisSiteSummaryLimitToExecNames");

         //Person reports

         VindisSalesmanReportLimitToSiteIds = GetIntArray("AppSettings", "vindisSalesmanReportLimitToSiteIds");
         VindisSalesmanReportLimitToExecNames = GetStringArray("AppSettings", "vindisSalesmanReportLimitToExecNames");

         //------------------------------------
         //RRG Commission Stuff
         //------------------------------------
         RRGSendReportsOnlyToMe = GetBool("AppSettings", "rrgSendReportsOnlyToMe");
         RRGMonthToRunFor = GetDate("AppSettings", "rrgMonthToRunFor");
         RRGIncludePriorMonthAdjustments = GetBool("AppSettings", "rrgIncludePriorMonthAdjustments");


         //Site reports

         RRGSiteSummaryDoSites = GetBool("AppSettings", "rrgSiteSummaryDoSites");
         RRGSiteSummaryDoTotal = GetBool("AppSettings", "rrgSiteSummaryDoTotal");
         RRGSiteSummaryLimitToSiteIds = GetIntArray("AppSettings", "rrgSiteSummaryLimitToSiteIds");
         RRGSiteSummaryLimitToExecNames = GetStringArray("AppSettings", "rrgSiteSummaryLimitToExecNames");

         //Person reports

         RRGSalesmanReportLimitToSiteIds = GetIntArray("AppSettings", "rrgSalesmanReportLimitToSiteIds");
         RRGSalesmanReportLimitToExecNames = GetStringArray("AppSettings", "rrgSalesmanReportLimitToExecNames");


         //------------------------------------
         //Other stuff
         //------------------------------------

         //Usage report
         ReportUsageStatsJobCronSchedule = GetString("AppSettings", "reportUsageStatsJobCronSchedule");
         UseageReportTo = GetStringArray("AppSettings", "useageReportTo");
         UseageReportCc = GetStringArray("AppSettings", "useageReportCc");
         EnterpriseLoadReportTo = GetStringArray("AppSettings", "enterpriseLoadReportTo");


         // GardX
         GardXJobCronSchedulePriorMonth = GetString("AppSettings", "gardXJobCronSchedulePriorMonth");
         GardXJobCronScheduleCurrentMonthToDate = GetString("AppSettings", "gardXJobCronScheduleCurrentMonthToDate");
         GardXReportTo = GetStringArray("AppSettings", "gardXReportTo");
         GardXReportCc = GetStringArray("AppSettings", "gardXReportCc");

         // Pinewood
         PinewoodMantlesUsername = GetString("AppSettings", "pinewoodMantlesUsername");
         PinewoodMantlesPassword = GetString("AppSettings", "pinewoodMantlesPassword");
         PinewoodMantlesOrgId = GetString("AppSettings", "pinewoodMantlesOrgId");
         PinewoodMantlesFilePath = GetString("AppSettings", "pinewoodMantlesFilePath");

         //Autotrader
         LocalBargainJobCronSchedule = GetString("AppSettings", "localBargainJobCronSchedule");
         VehicleValuationJobCronSchedule = GetString("AppSettings", "vehicleValuationJobCronSchedule");
         VehicleValuationCheckerJobCronSchedule = GetString("AppSettings", "vehicleValuationCheckerJobCronSchedule");
         EMGMantlesStockJobCronSchedule = GetString("AppSettings", "emgMantlesStockJobCronSchedule");

         //FetchAutoTraderPricingForDemoJobCronSchedule = GetString("AppSettings", "fetchAutoTraderPricingForDemoJobCronSchedule");
         FetchAutoTraderPricingJobCronSchedule = GetString("AppSettings", "fetchAutoTraderPricingJobCronSchedule");
         FetchAutoTraderPricingSecondRunJobCronSchedule = GetString("AppSettings", "fetchAutoTraderPricingSecondRunJobCronSchedule");
         UpdateAutoTraderPricesJobCronSchedule = GetString("AppSettings", "updateAutoTraderPricesJobCronSchedule");
         UpdateAutoTraderPrices4PMJobCronSchedule = GetString("AppSettings", "UpdateAutoTraderPrices4PMJobCronSchedule");
         updateAutoTraderPricesOnDemandJobCronSchedule = GetString("AppSettings", "updateAutoTraderPricesOnDemandJobCronSchedule");
         getPrivateAdsJobCronSchedule = GetString("AppSettings", "GetPrivateAdsJobCronSchedule");
         getTradeAdsJobCronSchedule = GetString("AppSettings", "GetTradeAdsJobCronSchedule");
         sendScheduledReportsJobCronSchedule = GetString("AppSettings", "sendScheduledReportsJobCronSchedule");
         AutotraderApiKey = GetString("AppSettings", "AutotraderApiKey");
         AutotraderApiSecret = GetString("AppSettings", "AutotraderApiSecret");
         AutotraderBaseURL = GetString("AppSettings", "AutotraderBaseURL");
         AutotraderBaseURLForChangingPrices = GetString("AppSettings", "AutotraderBaseURLForChangingPrices");

         RallyeApiKey = GetString("AppSettings", "RallyeApiKey");
         RallyeApiSecret = GetString("AppSettings", "RallyeApiSecret");
         RallyeBaseURL = GetString("AppSettings", "RallyeBaseURL");

         ErrorEmailTo = GetStringArray("AppSettings", "errorEmailTo");
         ValuationErrorEmailTo = GetStringArray("AppSettings", "valuationErrorEmailTo");

         ValuationChunkSize = GetInt("AppSettings", "valuationChunkSize");

         //DVSASettings
         DVSATokenURL = GetString("DVSASettings", "TokenURL");
         DVSABaseURLForRecallApi = GetString("DVSASettings", "BaseURLForRecallApi");
         DVSAApiKey = GetString("DVSASettings", "ApiKey");
         DVSAClientId = GetString("DVSASettings", "ClientId");
         DVSAClientSecret = GetString("DVSASettings", "ClientSecret");
         DVSAScope = GetString("DVSASettings", "Scope");
      }



      public static string GetConnectionString(DealerGroupName dealerGroup)
      {
         string dbConnectionName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
         switch (dbConnectionName)
         {
            case "RRGUKConnection":
               return ConfigService.RRGUKConnectionString;
            case "VindisConnection":
               return ConfigService.VindisConnectionString;
            case "RRGSpainConnection":
               return ConfigService.RRGSpainConnectionString;
            case "AutoPriceConnection":
            case "DefaultConnection":
               return ConfigService.AutoPriceConnectionString;
            case "SytnerConnection":
               return ConfigService.SytnerConnectionString;
            default:
               {
                  throw new Exception("Unknown dbConnectionName");
               }
         }
      }

      private static bool GetBool(string sectionName, string propertyName)
      {
         string stringVal = GetString(sectionName, propertyName);
         return stringVal == "True";
      }

      private static DateTime GetDate(string sectionName, string propertyName)
      {
         string dateString = GetString(sectionName, propertyName);
         int year = int.Parse(dateString.Split(',')[0]);
         int month = int.Parse(dateString.Split(',')[1]);
         int day = int.Parse(dateString.Split(',')[2]);
         return new DateTime(year, month, day);
      }


      private static List<string> GetStringArray(string sectionName, string propertyName)
      {
         string propString = GetString(sectionName, propertyName);
         if (propString != null && propString != string.Empty)
         {
            return propString.Split(',').ToList();
         }
         else
         {
            return new List<string>();
         }
      }
      private static List<int> GetIntArray(string sectionName, string propertyName)
      {
         string propString = GetString(sectionName, propertyName);
         if (propString != null && propString != string.Empty)
         {
            return propString.Split(',').Select(x => int.Parse(x)).ToList();
         }
         else
         {
            return new List<int>();
         }
      }

      private static int GetInt(string sectionName, string propertyName)
      {
         string propString = GetString(sectionName, propertyName);
         if (propString != null && propString != string.Empty)
         {
            return int.Parse(GetString(sectionName, propertyName));
         }
         else
         {
            return 0;
         }
      }


      private static string GetString(string sectionName, string propertyName)
      {
         return configurationBuilder.Build().GetSection(sectionName)[propertyName];
      }

      public static List<DealerGroupName> BuildEnumList(string setting)
      {
         if (setting == string.Empty)
         {
            return new List<DealerGroupName>();
         }

         return setting
             .Split(',')
             .Select(enumName => Enum.Parse<DealerGroupName>(enumName))
             .ToList();
      }





   }
}
